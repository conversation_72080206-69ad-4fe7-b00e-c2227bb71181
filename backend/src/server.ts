import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { connectDB } from './config/database';
import { getAppConfig } from './config/app';
import authRoutes from './routes/authRoutes';
import interviewRoutes from './routes/interviewRoutes';
import buildRoutes from './routes/buildRoutes';
import projectRoutes from './routes/projectRoutes';
import workspaceRoutes from './routes/workspaceRoutes';
import ecsWorkspaceRoutes from './routes/ecsWorkspaceRoutes';
import llmRoutes from './routes/llmRoutes';
import conversationRoutes from './routes/conversationRoutes';
import testRoutes from './routes/testRoutes';
import lifecycleRoutes from './routes/lifecycleRoutes';
import userRoutes from './routes/userRoutes';
import configRoutes from './routes/configRoutes';
import { workspaceCleanupService } from './services/workspaceCleanupService';
import { validateAWSConfig } from './config/aws';
import { log } from './utils/logger';

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();
const port = getAppConfig().port;

// Initialize server with proper async handling
async function initializeServer() {
  try {
    // Connect to MongoDB first
    log.info('Connecting to MongoDB...');
    await connectDB();
    log.success('MongoDB connected successfully');

    // Validate AWS configuration and start cleanup service
    try {
      if (validateAWSConfig()) {
        log.success('AWS configuration validated');
        workspaceCleanupService.start();
        log.success('Workspace cleanup service started');
      } else {
        log.warning('AWS configuration invalid - ECS workspace features disabled');
      }
    } catch (error) {
      log.warning('AWS configuration error - ECS workspace features disabled:', (error as Error).message);
    }

    // Start the server - bind to all interfaces for container compatibility
    const appConfig = getAppConfig();
    app.listen(port, '0.0.0.0', () => {
      log.info(`🚀 Server running on port ${port} (all interfaces) - DEBUG MODE`);
      log.info(`📊 Environment: ${appConfig.nodeEnv}`);
      log.info(`🔗 MongoDB: ${appConfig.mongodbUri}`);
      log.info(`🤖 LLM Bypass Mode: ${appConfig.bypassApiKey}`);
      log.info(`🌐 CORS: Enabled for all origins in ${appConfig.nodeEnv} mode`);
    });

  } catch (error) {
    log.failure('Failed to initialize server:', error);
    process.exit(1);
  }
}

// Enhanced CORS configuration for better cross-origin support
const corsOptions = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow all origins in development
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    // In production, you might want to restrict origins
    // For now, allow all origins but log them
    console.log(`🌐 CORS request from origin: ${origin}`);
    return callback(null, true);
  },
  credentials: false,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  maxAge: 86400 // 24 hours
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/interview', interviewRoutes);
app.use('/api/build', buildRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/workspace', workspaceRoutes);
app.use('/api/ecs-workspace', ecsWorkspaceRoutes);
app.use('/api/llm', llmRoutes);
app.use('/api/conversation', conversationRoutes);
app.use('/api/test', testRoutes);
app.use('/api/lifecycle', lifecycleRoutes);
app.use('/api/user', userRoutes);
app.use('/api/config', configRoutes);

// Basic health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
	res.json({
		status: 'ok',
		mode: 'mock-testing-with-payload-logging',
		timestamp: new Date().toISOString()
	});
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
	log.error(err.stack || err.message);
	res.status(500).json({ message: 'Something went wrong!' });
});

// Start the server initialization
initializeServer().catch((error) => {
  log.failure('Critical error during server initialization:', error);
  process.exit(1);
});