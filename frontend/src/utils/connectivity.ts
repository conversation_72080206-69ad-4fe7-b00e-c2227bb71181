// Connectivity utilities for testing backend availability
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export interface ConnectivityTestResult {
  success: boolean;
  latency?: number;
  error?: string;
  timestamp: string;
  endpoint: string;
}

/**
 * Test basic connectivity to the backend server
 */
export const testBackendConnectivity = async (): Promise<ConnectivityTestResult> => {
  const startTime = Date.now();
  const endpoint = `${API_BASE_URL}/api/health`;
  
  try {
    const response = await axios.get(endpoint, {
      timeout: 5000, // 5 second timeout for connectivity test
      validateStatus: (status) => status >= 200 && status < 500 // Accept any non-server error
    });
    
    const latency = Date.now() - startTime;
    
    return {
      success: response.status >= 200 && response.status < 300,
      latency,
      timestamp: new Date().toISOString(),
      endpoint
    };
  } catch (error: any) {
    const latency = Date.now() - startTime;
    
    return {
      success: false,
      latency,
      error: error.code === 'ECONNREFUSED' 
        ? 'Connection refused - backend not running'
        : error.code === 'ECONNABORTED'
        ? 'Connection timeout - backend not responding'
        : error.message || 'Unknown error',
      timestamp: new Date().toISOString(),
      endpoint
    };
  }
};

/**
 * Test connectivity with retry logic
 */
export const testBackendConnectivityWithRetry = async (
  maxRetries: number = 3,
  retryDelay: number = 2000
): Promise<ConnectivityTestResult> => {
  let lastResult: ConnectivityTestResult;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`🔍 Testing backend connectivity (attempt ${attempt}/${maxRetries})...`);
    
    lastResult = await testBackendConnectivity();
    
    if (lastResult.success) {
      console.log(`✅ Backend connectivity test passed on attempt ${attempt}`);
      return lastResult;
    }
    
    console.warn(`❌ Backend connectivity test failed on attempt ${attempt}:`, lastResult.error);
    
    if (attempt < maxRetries) {
      console.log(`⏳ Retrying in ${retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  console.error(`🚫 Backend connectivity test failed after ${maxRetries} attempts`);
  return lastResult!;
};

/**
 * Monitor backend connectivity continuously
 */
export const monitorBackendConnectivity = (
  onStatusChange: (result: ConnectivityTestResult) => void,
  interval: number = 30000 // 30 seconds
): () => void => {
  let isMonitoring = true;
  let lastStatus: boolean | null = null;
  
  const checkConnectivity = async () => {
    if (!isMonitoring) return;
    
    const result = await testBackendConnectivity();
    
    // Only call callback if status changed or this is the first check
    if (lastStatus === null || lastStatus !== result.success) {
      lastStatus = result.success;
      onStatusChange(result);
    }
    
    if (isMonitoring) {
      setTimeout(checkConnectivity, interval);
    }
  };
  
  // Start monitoring
  checkConnectivity();
  
  // Return stop function
  return () => {
    isMonitoring = false;
  };
};

/**
 * Get backend configuration and status
 */
export const getBackendStatus = async (): Promise<{
  config: any;
  connectivity: ConnectivityTestResult;
}> => {
  const connectivity = await testBackendConnectivity();
  
  let config = null;
  if (connectivity.success) {
    try {
      const configResponse = await axios.get(`${API_BASE_URL}/api/config`, {
        timeout: 5000
      });
      config = configResponse.data;
    } catch (error) {
      console.warn('Failed to fetch backend config:', error);
    }
  }
  
  return {
    config,
    connectivity
  };
};
