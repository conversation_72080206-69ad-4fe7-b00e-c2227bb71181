# ECS Workspace Networking & Connectivity Guide

## Overview
This guide addresses frontend-backend connectivity issues in ECS workspaces and provides robust solutions for reliable communication between services.

## Key Improvements Made

### 1. Port Configuration
- **Backend**: Now runs on port `8000` (updated from `3001`)
- **Frontend**: Configured to connect to `localhost:8000`
- **Environment Variables**: Properly configured in `.env` files

### 2. Network Binding
- **Backend Server**: Binds to `0.0.0.0:8000` (all interfaces) instead of `localhost`
- **Frontend Dev Server**: Binds to `0.0.0.0:8080` for container compatibility
- **Workspace Server**: Enhanced CORS headers and network configuration

### 3. Enhanced Error Handling
- **Connection Retry Logic**: Backend startup retries up to 3 times on failure
- **Timeout Configuration**: 30-second timeout for API requests
- **Enhanced Logging**: Detailed error messages for network issues
- **Port Listening Verification**: Automatic verification that services are actually listening

### 4. CORS Configuration
- **Comprehensive Headers**: Added all necessary CORS headers
- **Development Mode**: Allows all origins in development
- **Preflight Handling**: Proper OPTIONS request handling

## Configuration Files Updated

### Backend Configuration (`backend/.env`)
```bash
PORT=8000  # Changed from 3001
NODE_ENV=development
```

### Frontend Configuration (`frontend/.env`)
```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_MOCK_MODE=false
```

### API Configuration (`frontend/src/utils/api.ts`)
- Default backend URL: `http://localhost:8000`
- 30-second timeout for requests
- Enhanced error logging for network issues
- Better connection failure detection

## Troubleshooting Common Issues

### Issue 1: "Connection Refused" Errors
**Symptoms**: Frontend cannot reach backend API
**Solutions**:
1. Verify backend is running: `curl http://localhost:8000/api/health`
2. Check backend logs for startup errors
3. Ensure backend binds to `0.0.0.0` not `localhost`
4. Verify port 8000 is not blocked by firewall

### Issue 2: Intermittent Connectivity
**Symptoms**: Sometimes works, sometimes doesn't
**Solutions**:
1. Check backend startup timing - may need more time to initialize
2. Use the connectivity test utility: `testBackendConnectivityWithRetry()`
3. Monitor backend process status in ECS workspace
4. Check for port conflicts with other services

### Issue 3: CORS Errors
**Symptoms**: Browser blocks requests due to CORS policy
**Solutions**:
1. Verify CORS headers are set correctly
2. Check that backend allows the frontend origin
3. Ensure preflight OPTIONS requests are handled
4. Use browser dev tools to inspect CORS headers

## Testing Connectivity

### Manual Testing
```bash
# Test backend health endpoint
curl http://localhost:8000/api/health

# Test ECS workspace backend status
curl http://localhost:3000/api/backend/test

# Check if backend port is listening
netstat -tlnp | grep :8000
```

### Frontend Testing
```typescript
import { testBackendConnectivityWithRetry } from '@/utils/connectivity';

// Test connectivity with retry
const result = await testBackendConnectivityWithRetry(3, 2000);
console.log('Backend connectivity:', result);
```

## ECS Workspace API Endpoints

### Backend Management
- `GET /api/backend/status` - Check backend service status
- `GET /api/backend/logs` - View backend service logs
- `POST /api/backend/stop` - Stop backend service
- `GET /api/backend/test` - Test backend connectivity

### Preview Management
- `POST /api/preview/start` - Start frontend and backend services
- `GET /api/preview/status` - Check service status
- `POST /api/preview/stop` - Stop all services

## Best Practices

### 1. Service Startup Order
1. Start workspace server (port 3000)
2. Start backend server (port 8000)
3. Start frontend dev server (port 8080)

### 2. Health Checks
- Always verify services are listening on expected ports
- Use health endpoints to confirm service readiness
- Implement retry logic for service dependencies

### 3. Error Handling
- Log detailed error information for debugging
- Implement graceful degradation for service failures
- Provide clear error messages to users

### 4. Development Workflow
1. Check backend connectivity before making API calls
2. Use the connectivity monitoring utility during development
3. Monitor service logs for startup issues
4. Test with both mock and real backend modes

## Monitoring and Debugging

### Log Locations
- **Backend Logs**: Check ECS workspace `/api/backend/logs`
- **Frontend Logs**: Browser console and network tab
- **Workspace Logs**: ECS workspace server console

### Debug Commands
```bash
# Check service status
curl http://localhost:3000/api/backend/status

# View recent logs
curl http://localhost:3000/api/backend/logs

# Test connectivity
curl http://localhost:3000/api/backend/test
```

## Environment Variables Reference

### Required Variables
- `PORT`: Backend server port (8000)
- `VITE_API_BASE_URL`: Frontend API base URL
- `NODE_ENV`: Environment mode (development/production)

### Optional Variables
- `VITE_MOCK_MODE`: Enable mock mode for testing
- `CORS_ORIGIN`: Allowed CORS origins
- `BIND_ADDRESS`: Server bind address (0.0.0.0)

## Support and Troubleshooting

If you continue to experience connectivity issues:

1. **Check Service Status**: Use the ECS workspace API endpoints
2. **Review Logs**: Check both frontend and backend logs
3. **Test Connectivity**: Use the provided connectivity utilities
4. **Verify Configuration**: Ensure all environment variables are correct
5. **Restart Services**: Stop and restart both frontend and backend services

For persistent issues, the enhanced logging and retry mechanisms should provide detailed information about the root cause of connectivity problems.
